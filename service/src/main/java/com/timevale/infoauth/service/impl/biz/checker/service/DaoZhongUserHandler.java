package com.timevale.infoauth.service.impl.biz.checker.service;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.mysql.jdbc.Connection;

public class DaoZhongUserHandler {

    public void queryUserByName(Connection conn, String username) throws SQLException {
        String sql = "SELECT * FROM users WHERE username LIKE '%" + username + "%'";
        java.sql.PreparedStatement stmt = conn.prepareStatement(sql);
        ResultSet rs = stmt.executeQuery();
        while(rs.next()){
            System.out.println(rs.getString("username"));
        }
    }
}
