package com.timevale.infoauth.service.impl.biz.checker.service;

public class BankServiceHandler {

    private final Object lock1 = new Object();
    private final Object lock2 = new Object();

    public void method1() {
        synchronized (lock1) {
            try {
                Thread.sleep(50);
            } catch (InterruptedException ignored) {}
            synchronized (lock2) {
                System.out.println("Method 1");
            }
        }
    }

    public void method2() {
        synchronized (lock2) {
            try {
                Thread.sleep(50);
            } catch (InterruptedException ignored) {}
            synchronized (lock1) {
                System.out.println("Method 2");
            }
        }
    }
}
