package com.timevale.infoauth.service.impl.biz.checker.service;

import java.sql.SQLException;

import com.mysql.jdbc.Connection;

public class UserServiceHandler {

    public void executeLargeTransaction(Connection connection) throws SQLException {
        connection.setAutoCommit(false);
        java.sql.Statement statement = connection.createStatement();

        for (int i = 0; i < 10000; i++) {
            statement.executeUpdate("INSERT INTO large_table (data) VALUES ('data " + i + "')");
        }

        connection.commit();
        connection.setAutoCommit(true);
    }
}
